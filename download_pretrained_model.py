#!/usr/bin/env python3
"""
Download Pre-trained Stock Market Pattern Detection Model

This script downloads the foduucom/stockmarket-pattern-detection-yolov8 model
from Hugging Face and sets it up for use with our YOLO11 environment.
"""

import os
import sys
from pathlib import Path
import requests
import urllib3
from urllib.parse import urljoin

# Disable SSL warnings for this download
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def download_file(url, local_path, verify_ssl=True):
    """Download a file from URL to local path."""
    try:
        print(f"🔄 Downloading {url}")
        response = requests.get(url, verify=verify_ssl, stream=True)
        response.raise_for_status()
        
        # Create directory if it doesn't exist
        Path(local_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Download with progress
        total_size = int(response.headers.get('content-length', 0))
        downloaded = 0
        
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded += len(chunk)
                    if total_size > 0:
                        progress = (downloaded / total_size) * 100
                        print(f"\r📥 Progress: {progress:.1f}%", end='', flush=True)
        
        print(f"\n✅ Downloaded: {local_path}")
        return True
        
    except Exception as e:
        print(f"\n❌ Failed to download {url}: {e}")
        return False

def download_huggingface_model():
    """Download the stock market pattern detection model from Hugging Face."""
    
    base_url = "https://huggingface.co/foduucom/stockmarket-pattern-detection-yolov8/resolve/main/"
    output_dir = Path("models/stockmarket_patterns")
    
    # Common files that might be in the repository
    files_to_try = [
        "best.pt",
        "yolov8_stockpattern.pt", 
        "model.pt",
        "weights.pt",
        "README.md",
        "config.yaml",
        "classes.txt",
        "requirements.txt"
    ]
    
    print("🚀 Downloading Stock Market Pattern Detection Model")
    print("=" * 60)
    
    downloaded_files = []
    
    for filename in files_to_try:
        url = urljoin(base_url, filename)
        local_path = output_dir / filename
        
        # Try with SSL verification first, then without
        if download_file(url, local_path, verify_ssl=True):
            downloaded_files.append(filename)
        elif download_file(url, local_path, verify_ssl=False):
            downloaded_files.append(filename)
    
    if downloaded_files:
        print(f"\n🎉 Successfully downloaded {len(downloaded_files)} files:")
        for file in downloaded_files:
            print(f"  ✅ {file}")
        
        print(f"\n📁 Files saved to: {output_dir.absolute()}")
        return True
    else:
        print("\n❌ No files could be downloaded")
        return False

def try_huggingface_hub():
    """Try using the huggingface_hub library."""
    try:
        print("🔄 Trying huggingface_hub library...")
        
        # Try to import and use huggingface_hub
        from huggingface_hub import hf_hub_download, list_repo_files
        
        repo_id = "foduucom/stockmarket-pattern-detection-yolov8"
        output_dir = Path("models/stockmarket_patterns")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # List available files
        print("📋 Listing repository files...")
        files = list_repo_files(repo_id)
        print(f"Found {len(files)} files in repository:")
        for file in files:
            print(f"  📄 {file}")
        
        # Download all files
        downloaded_files = []
        for filename in files:
            try:
                local_path = hf_hub_download(
                    repo_id=repo_id,
                    filename=filename,
                    local_dir=output_dir,
                    local_dir_use_symlinks=False
                )
                downloaded_files.append(filename)
                print(f"✅ Downloaded: {filename}")
            except Exception as e:
                print(f"❌ Failed to download {filename}: {e}")
        
        if downloaded_files:
            print(f"\n🎉 Successfully downloaded {len(downloaded_files)} files using huggingface_hub")
            return True
        
    except ImportError:
        print("❌ huggingface_hub not installed. Install with: pip install huggingface_hub")
    except Exception as e:
        print(f"❌ huggingface_hub method failed: {e}")
    
    return False

def manual_download_instructions():
    """Provide manual download instructions."""
    print("\n" + "=" * 60)
    print("📋 MANUAL DOWNLOAD INSTRUCTIONS")
    print("=" * 60)
    print("\nIf automatic download fails, you can manually download the model:")
    print("\n1. Visit: https://huggingface.co/foduucom/stockmarket-pattern-detection-yolov8")
    print("2. Click on 'Files and versions' tab")
    print("3. Download the model files (usually .pt files)")
    print("4. Place them in: models/stockmarket_patterns/")
    print("\nAlternatively, try these commands:")
    print("```bash")
    print("# Install huggingface_hub")
    print("pip install huggingface_hub")
    print("")
    print("# Download using git with LFS")
    print("git lfs install")
    print("git clone https://huggingface.co/foduucom/stockmarket-pattern-detection-yolov8")
    print("```")

def main():
    """Main function to download the model."""
    print("📈 Stock Market Pattern Detection Model Downloader")
    print("=" * 60)
    
    # Method 1: Try huggingface_hub library
    if try_huggingface_hub():
        return
    
    # Method 2: Direct HTTP download
    if download_huggingface_model():
        return
    
    # Method 3: Manual instructions
    manual_download_instructions()

if __name__ == "__main__":
    main()
