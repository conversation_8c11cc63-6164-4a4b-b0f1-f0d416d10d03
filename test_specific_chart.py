#!/usr/bin/env python3
"""
Test specific chart image for pattern detection
"""

from simple_chart_detector import SimpleChartDetector
import json

def test_chart(image_path):
    """Test a specific chart image."""
    print(f"🔍 Testing chart: {image_path}")
    print("=" * 60)
    
    # Initialize detector
    detector = SimpleChartDetector()
    
    # Analyze the chart
    results = detector.analyze_image(image_path)
    
    # Display detailed results
    print(f"\n📊 Analysis Results:")
    print(f"Image: {results['image_path']}")
    print(f"Patterns detected: {results['patterns_detected']}")
    
    if results['patterns']:
        print(f"\n🎯 Detected Patterns:")
        for i, pattern in enumerate(results['patterns'], 1):
            print(f"  {i}. {pattern['pattern']}")
            print(f"     Confidence: {pattern['confidence']:.2f}")
            print(f"     Signal: {pattern['signal']} ({pattern['strength']})")
            print(f"     Bbox: {pattern['bbox']}")
    
    print(f"\n📈 Trading Signals:")
    signals = results['trading_signals']
    print(f"Overall Signal: {signals['overall_signal']}")
    print(f"Confidence: {signals['confidence']:.2f}")
    print(f"Risk Level: {signals['risk_level']}")
    print(f"Recommendation: {signals['recommendation']}")
    
    return results

if __name__ == "__main__":
    # Test the specific chart
    chart_path = "data/datasets/CL1!_2025-06-15_17-23-20.png"
    results = test_chart(chart_path)
