#!/usr/bin/env python3
"""
Simple test script for the chart pattern detection model
"""

import sys
from pathlib import Path

def test_model_loading():
    """Test if the model can be loaded."""
    try:
        from ultralytics import YOLO
        
        model_path = "models/stockmarket_patterns/model.pt"
        if not Path(model_path).exists():
            print(f"❌ Model file not found: {model_path}")
            return False
        
        print(f"🔄 Loading model: {model_path}")
        model = YOLO(model_path)
        print("✅ Model loaded successfully!")
        
        # Print model info
        print(f"📊 Model info:")
        print(f"  - Model type: {type(model.model)}")
        print(f"  - Classes: {model.names}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return False

def test_basic_inference():
    """Test basic inference on a sample image."""
    try:
        from ultralytics import YOLO
        import cv2
        import numpy as np
        
        # Load model
        model = YOLO("models/stockmarket_patterns/model.pt")
        
        # Create a simple test image (black square)
        test_image = np.zeros((640, 640, 3), dtype=np.uint8)
        test_path = "test_image.png"
        cv2.imwrite(test_path, test_image)
        
        print("🔄 Running inference on test image...")
        results = model(test_path, verbose=False)
        
        print("✅ Inference completed!")
        print(f"📊 Results: {len(results)} result(s)")
        
        # Clean up
        Path(test_path).unlink(missing_ok=True)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in inference: {e}")
        return False

def main():
    """Run tests."""
    print("🧪 Testing Chart Pattern Detection Model")
    print("=" * 50)
    
    # Test 1: Model loading
    print("\n1. Testing model loading...")
    if not test_model_loading():
        print("❌ Model loading failed. Exiting.")
        sys.exit(1)
    
    # Test 2: Basic inference
    print("\n2. Testing basic inference...")
    if not test_basic_inference():
        print("❌ Basic inference failed.")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("🎉 All tests passed! The model is working correctly.")
    print("\n📚 Next steps:")
    print("1. Try the full pattern detector: python chart_patterns/pattern_detector.py")
    print("2. Analyze real stock data")
    print("3. Integrate with trading strategies")

if __name__ == "__main__":
    main()
