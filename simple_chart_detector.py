#!/usr/bin/env python3
"""
Simple Chart Pattern Detector

A simplified version that works with existing chart images
and demonstrates the pre-trained stock market pattern detection model.
"""

import cv2
import numpy as np
import json
from pathlib import Path
from datetime import datetime
from typing import List, Dict
from ultralytics import YOLO

class SimpleChartDetector:
    """Simple chart pattern detector for existing images."""
    
    def __init__(self, model_path: str = "models/stockmarket_patterns/model.pt"):
        """Initialize the detector."""
        self.model = YOLO(model_path)
        self.pattern_classes = {
            0: 'Head and shoulders bottom',
            1: 'Head and shoulders top', 
            2: 'M_Head',
            3: 'StockLine',
            4: 'Triangle',
            5: 'W_Bottom'
        }
        
        self.pattern_signals = {
            'Head and shoulders bottom': {'signal': 'bullish', 'strength': 'strong'},
            'Head and shoulders top': {'signal': 'bearish', 'strength': 'strong'},
            'M_Head': {'signal': 'bearish', 'strength': 'medium'},
            'StockLine': {'signal': 'neutral', 'strength': 'weak'},
            'Triangle': {'signal': 'neutral', 'strength': 'medium'},
            'W_Bottom': {'signal': 'bullish', 'strength': 'medium'}
        }
        
        print("✅ Chart Pattern Detector initialized!")
        print(f"📊 Supported patterns: {list(self.pattern_classes.values())}")
    
    def detect_patterns(self, image_path: str, confidence: float = 0.25) -> List[Dict]:
        """Detect patterns in a chart image."""
        print(f"🔍 Analyzing: {image_path}")
        
        # Run inference
        results = self.model(image_path, conf=confidence, save=True)
        
        patterns = []
        if results and results[0].boxes is not None:
            for box in results[0].boxes:
                class_id = int(box.cls[0])
                conf = float(box.conf[0])
                bbox = box.xyxy[0].tolist()
                
                pattern_name = self.pattern_classes[class_id]
                signal_info = self.pattern_signals[pattern_name]
                
                pattern = {
                    'pattern': pattern_name,
                    'confidence': conf,
                    'bbox': bbox,
                    'signal': signal_info['signal'],
                    'strength': signal_info['strength'],
                    'timestamp': datetime.now().isoformat()
                }
                patterns.append(pattern)
                
                print(f"  ✅ {pattern_name}: {conf:.2f} confidence ({signal_info['signal']})")
        else:
            print("  ❌ No patterns detected")
        
        return patterns
    
    def create_sample_chart(self, save_path: str = "sample_chart.png"):
        """Create a sample chart for testing."""
        # Create a simple candlestick-like chart
        img = np.ones((640, 640, 3), dtype=np.uint8) * 255  # White background
        
        # Draw some sample candlesticks
        for i in range(10, 600, 40):
            # Random candlestick data
            open_price = np.random.randint(200, 400)
            close_price = open_price + np.random.randint(-50, 50)
            high_price = max(open_price, close_price) + np.random.randint(0, 30)
            low_price = min(open_price, close_price) - np.random.randint(0, 30)
            
            # Draw candlestick
            color = (0, 255, 0) if close_price > open_price else (0, 0, 255)
            
            # High-low line
            cv2.line(img, (i, high_price), (i, low_price), (0, 0, 0), 2)
            
            # Body
            cv2.rectangle(img, (i-10, open_price), (i+10, close_price), color, -1)
            cv2.rectangle(img, (i-10, open_price), (i+10, close_price), (0, 0, 0), 1)
        
        cv2.imwrite(save_path, img)
        print(f"📊 Sample chart created: {save_path}")
        return save_path
    
    def analyze_image(self, image_path: str, save_results: bool = True) -> Dict:
        """Complete analysis of a chart image."""
        print(f"🚀 Analyzing chart image: {image_path}")
        print("=" * 50)
        
        # Detect patterns
        patterns = self.detect_patterns(image_path)
        
        # Generate trading signals
        signals = self.generate_signals(patterns)
        
        # Compile results
        results = {
            'image_path': image_path,
            'analysis_date': datetime.now().isoformat(),
            'patterns_detected': len(patterns),
            'patterns': patterns,
            'trading_signals': signals
        }
        
        # Save results
        if save_results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            results_path = f"results/chart_analysis_{timestamp}.json"
            Path(results_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(results_path, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Results saved: {results_path}")
        
        return results
    
    def generate_signals(self, patterns: List[Dict]) -> Dict:
        """Generate trading signals from detected patterns."""
        if not patterns:
            return {
                'overall_signal': 'neutral',
                'confidence': 0.0,
                'recommendation': 'No patterns detected',
                'risk_level': 'low'
            }
        
        # Calculate weighted signals
        bullish_weight = 0
        bearish_weight = 0
        total_confidence = 0
        
        strength_weights = {'weak': 1, 'medium': 2, 'strong': 3}
        
        for pattern in patterns:
            conf = pattern['confidence']
            strength = strength_weights[pattern['strength']]
            weight = conf * strength
            
            if pattern['signal'] == 'bullish':
                bullish_weight += weight
            elif pattern['signal'] == 'bearish':
                bearish_weight += weight
            
            total_confidence += conf
        
        # Determine overall signal
        if bullish_weight > bearish_weight * 1.2:
            overall_signal = 'bullish'
            signal_strength = bullish_weight / (bullish_weight + bearish_weight)
        elif bearish_weight > bullish_weight * 1.2:
            overall_signal = 'bearish'
            signal_strength = bearish_weight / (bullish_weight + bearish_weight)
        else:
            overall_signal = 'neutral'
            signal_strength = 0.5
        
        # Generate recommendation
        avg_confidence = total_confidence / len(patterns)
        
        if overall_signal == 'bullish' and avg_confidence > 0.6:
            recommendation = "Strong bullish patterns detected. Consider long positions."
            risk_level = 'medium'
        elif overall_signal == 'bearish' and avg_confidence > 0.6:
            recommendation = "Strong bearish patterns detected. Consider short positions."
            risk_level = 'medium'
        elif overall_signal == 'bullish':
            recommendation = "Weak bullish signals. Monitor for confirmation."
            risk_level = 'low'
        elif overall_signal == 'bearish':
            recommendation = "Weak bearish signals. Monitor for confirmation."
            risk_level = 'low'
        else:
            recommendation = "Mixed signals. Wait for clearer patterns."
            risk_level = 'low'
        
        return {
            'overall_signal': overall_signal,
            'confidence': signal_strength,
            'recommendation': recommendation,
            'risk_level': risk_level,
            'pattern_count': len(patterns),
            'avg_confidence': avg_confidence
        }
    
    def batch_analyze_directory(self, directory: str, pattern: str = "*.png") -> Dict:
        """Analyze all images in a directory."""
        image_dir = Path(directory)
        if not image_dir.exists():
            raise FileNotFoundError(f"Directory not found: {directory}")
        
        image_files = list(image_dir.glob(pattern))
        if not image_files:
            print(f"❌ No images found in {directory} with pattern {pattern}")
            return {}
        
        print(f"🔄 Batch analyzing {len(image_files)} images...")
        
        batch_results = {
            'analysis_date': datetime.now().isoformat(),
            'directory': str(image_dir),
            'images_analyzed': len(image_files),
            'results': {}
        }
        
        for i, image_file in enumerate(image_files, 1):
            print(f"\n📊 Analyzing {image_file.name} ({i}/{len(image_files)})")
            try:
                result = self.analyze_image(str(image_file), save_results=False)
                batch_results['results'][image_file.name] = result
            except Exception as e:
                print(f"❌ Error analyzing {image_file.name}: {e}")
                batch_results['results'][image_file.name] = {'error': str(e)}
        
        # Save batch results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        batch_path = f"results/batch_analysis_{timestamp}.json"
        Path(batch_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(batch_path, 'w') as f:
            json.dump(batch_results, f, indent=2)
        
        print(f"\n🎉 Batch analysis complete! Results: {batch_path}")
        return batch_results

def main():
    """Demo the chart pattern detector."""
    print("📈 Simple Chart Pattern Detector Demo")
    print("=" * 60)
    
    # Initialize detector
    detector = SimpleChartDetector()
    
    # Create a sample chart for testing
    print("\n1. Creating sample chart...")
    sample_chart = detector.create_sample_chart("sample_chart.png")
    
    # Analyze the sample chart
    print("\n2. Analyzing sample chart...")
    results = detector.analyze_image(sample_chart)
    
    # Display results
    print(f"\n📊 Analysis Results:")
    print(f"Patterns detected: {results['patterns_detected']}")
    print(f"Overall signal: {results['trading_signals']['overall_signal']}")
    print(f"Recommendation: {results['trading_signals']['recommendation']}")
    
    # Check if there are any existing chart images to analyze
    print("\n3. Looking for existing chart images...")
    chart_dirs = ["data/images", "results", "."]
    
    for chart_dir in chart_dirs:
        if Path(chart_dir).exists():
            image_files = list(Path(chart_dir).glob("*.png")) + list(Path(chart_dir).glob("*.jpg"))
            if image_files:
                print(f"Found {len(image_files)} images in {chart_dir}")
                for img_file in image_files[:3]:  # Analyze first 3 images
                    print(f"\n📊 Analyzing: {img_file}")
                    try:
                        result = detector.analyze_image(str(img_file), save_results=False)
                        signals = result['trading_signals']
                        print(f"  Signal: {signals['overall_signal']} (confidence: {signals['confidence']:.2f})")
                        print(f"  Patterns: {result['patterns_detected']}")
                    except Exception as e:
                        print(f"  ❌ Error: {e}")
                break
    
    print("\n" + "=" * 60)
    print("🎉 Demo complete!")
    print("\n📚 Usage examples:")
    print("1. detector.analyze_image('path/to/chart.png')")
    print("2. detector.batch_analyze_directory('charts_folder')")
    print("3. detector.create_sample_chart('test.png')")

if __name__ == "__main__":
    main()
