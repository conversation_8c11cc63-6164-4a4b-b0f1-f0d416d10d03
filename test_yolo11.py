#!/usr/bin/env python3
"""
YOLO11 Quick Test Script

This script performs a quick test to verify that YOLO11 is properly installed and working.
"""

import sys
from pathlib import Path

def test_imports():
    """Test if all required packages can be imported."""
    print("🔄 Testing imports...")
    try:
        import torch
        import torchvision
        import cv2
        import numpy as np
        import matplotlib.pyplot as plt
        from ultralytics import YOL<PERSON>
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_model_loading():
    """Test if YOLO11 model can be loaded."""
    print("🔄 Testing model loading...")
    try:
        from ultralytics import YOLO
        model = YOLO('yolo11n.pt')
        print("✅ Model loaded successfully!")
        return True, model
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False, None

def test_inference():
    """Test if inference works on sample image."""
    print("🔄 Testing inference...")
    try:
        from ultralytics import YOLO
        
        # Check if sample image exists
        sample_image = Path("data/images/bus.jpg")
        if not sample_image.exists():
            print(f"❌ Sample image not found: {sample_image}")
            return False
        
        # Load model and run inference
        model = YOLO('yolo11n.pt')
        results = model(str(sample_image), verbose=False)
        
        # Check results
        if results and len(results) > 0:
            detections = len(results[0].boxes) if results[0].boxes is not None else 0
            print(f"✅ Inference successful! Found {detections} objects")
            return True
        else:
            print("❌ No results returned from inference")
            return False
            
    except Exception as e:
        print(f"❌ Inference failed: {e}")
        return False

def test_gpu_availability():
    """Test if GPU is available for acceleration."""
    print("🔄 Testing GPU availability...")
    try:
        import torch
        if torch.cuda.is_available():
            gpu_count = torch.cuda.device_count()
            gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
            print(f"✅ GPU available: {gpu_name} ({gpu_count} device(s))")
            return True
        else:
            print("⚠️  GPU not available, using CPU")
            return False
    except Exception as e:
        print(f"❌ GPU test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 YOLO11 Installation Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Model loading
    success, model = test_model_loading()
    if success:
        tests_passed += 1
    
    # Test 3: Inference
    if test_inference():
        tests_passed += 1
    
    # Test 4: GPU (optional)
    if test_gpu_availability():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed >= 3:  # GPU is optional
        print("🎉 YOLO11 is properly installed and working!")
        print("\n📚 Next steps:")
        print("1. Try the examples in the examples/ directory")
        print("2. Read the documentation: https://docs.ultralytics.com/models/yolo11/")
        print("3. Start building your own computer vision applications!")
    else:
        print("❌ Some tests failed. Please check the installation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
