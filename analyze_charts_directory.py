#!/usr/bin/env python3
"""
Analyze all charts in a specific directory
"""

from simple_chart_detector import SimpleChartDetector
from pathlib import Path
import json

def analyze_charts_directory(charts_dir):
    """Analyze all charts in the specified directory."""
    charts_path = Path(charts_dir)

    if not charts_path.exists():
        print(f"❌ Directory not found: {charts_dir}")
        return

    print(f"📁 Analyzing charts in: {charts_dir}")
    print("=" * 80)

    # Initialize detector
    detector = SimpleChartDetector()

    # Find all image files
    image_extensions = ['*.png', '*.jpg', '*.jpeg', '*.bmp', '*.tiff']
    image_files = []

    for ext in image_extensions:
        image_files.extend(charts_path.glob(ext))
        image_files.extend(charts_path.glob(ext.upper()))

    # Remove duplicates (in case files are found by both lowercase and uppercase patterns)
    image_files = list(set(image_files))

    if not image_files:
        print(f"❌ No image files found in {charts_dir}")
        return

    print(f"🔍 Found {len(image_files)} chart images")
    print("-" * 80)

    # Analyze each chart
    all_results = {}
    summary_stats = {
        'total_charts': len(image_files),
        'charts_with_patterns': 0,
        'total_patterns': 0,
        'bullish_signals': 0,
        'bearish_signals': 0,
        'neutral_signals': 0,
        'pattern_types': {}
    }

    for i, image_file in enumerate(image_files, 1):
        print(f"\n📊 [{i}/{len(image_files)}] Analyzing: {image_file.name}")

        try:
            # Analyze the chart
            results = detector.analyze_image(str(image_file), save_results=False)
            all_results[image_file.name] = results

            # Update summary stats
            if results['patterns_detected'] > 0:
                summary_stats['charts_with_patterns'] += 1
                summary_stats['total_patterns'] += results['patterns_detected']

                # Count pattern types
                for pattern in results['patterns']:
                    pattern_name = pattern['pattern']
                    if pattern_name not in summary_stats['pattern_types']:
                        summary_stats['pattern_types'][pattern_name] = 0
                    summary_stats['pattern_types'][pattern_name] += 1

                # Count signals
                signal = results['trading_signals']['overall_signal']
                if signal == 'bullish':
                    summary_stats['bullish_signals'] += 1
                elif signal == 'bearish':
                    summary_stats['bearish_signals'] += 1
                else:
                    summary_stats['neutral_signals'] += 1

                # Display results
                print(f"  ✅ Patterns: {results['patterns_detected']}")
                print(f"  📈 Signal: {signal}")
                print(f"  💡 Recommendation: {results['trading_signals']['recommendation']}")

                # Show detected patterns
                for pattern in results['patterns']:
                    print(f"    - {pattern['pattern']}: {pattern['confidence']:.2f} confidence")
            else:
                print(f"  ❌ No patterns detected")
                summary_stats['neutral_signals'] += 1

        except Exception as e:
            print(f"  ❌ Error analyzing {image_file.name}: {e}")
            all_results[image_file.name] = {'error': str(e)}

    # Display summary
    print("\n" + "=" * 80)
    print("📊 ANALYSIS SUMMARY")
    print("=" * 80)
    print(f"Total charts analyzed: {summary_stats['total_charts']}")
    print(f"Charts with patterns: {summary_stats['charts_with_patterns']}")
    print(f"Total patterns found: {summary_stats['total_patterns']}")
    print(f"Detection rate: {(summary_stats['charts_with_patterns']/summary_stats['total_charts']*100):.1f}%")

    print(f"\n📈 Trading Signals:")
    print(f"  Bullish: {summary_stats['bullish_signals']}")
    print(f"  Bearish: {summary_stats['bearish_signals']}")
    print(f"  Neutral: {summary_stats['neutral_signals']}")

    if summary_stats['pattern_types']:
        print(f"\n🎯 Pattern Types Found:")
        for pattern_type, count in sorted(summary_stats['pattern_types'].items(), key=lambda x: x[1], reverse=True):
            print(f"  {pattern_type}: {count}")

    # Save detailed results
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"results/batch_charts_analysis_{timestamp}.json"

    Path(results_file).parent.mkdir(parents=True, exist_ok=True)

    final_results = {
        'analysis_date': datetime.now().isoformat(),
        'charts_directory': str(charts_path),
        'summary': summary_stats,
        'detailed_results': all_results
    }

    with open(results_file, 'w') as f:
        json.dump(final_results, f, indent=2)

    print(f"\n💾 Detailed results saved to: {results_file}")

    # Show top recommendations
    print(f"\n🏆 TOP RECOMMENDATIONS:")
    bullish_charts = []
    bearish_charts = []

    for chart_name, result in all_results.items():
        if 'error' not in result and result['patterns_detected'] > 0:
            signal = result['trading_signals']['overall_signal']
            confidence = result['trading_signals']['confidence']

            if signal == 'bullish':
                bullish_charts.append((chart_name, confidence, result['patterns_detected']))
            elif signal == 'bearish':
                bearish_charts.append((chart_name, confidence, result['patterns_detected']))

    # Sort by confidence
    bullish_charts.sort(key=lambda x: x[1], reverse=True)
    bearish_charts.sort(key=lambda x: x[1], reverse=True)

    if bullish_charts:
        print(f"\n📈 Top Bullish Opportunities:")
        for chart, conf, patterns in bullish_charts[:5]:
            print(f"  {chart}: {conf:.2f} confidence, {patterns} patterns")

    if bearish_charts:
        print(f"\n📉 Top Bearish Signals:")
        for chart, conf, patterns in bearish_charts[:5]:
            print(f"  {chart}: {conf:.2f} confidence, {patterns} patterns")

    return final_results

def main():
    """Main function."""
    # Your charts directory
    charts_directory = r"C:\Users\<USER>\Desktop\pythonicfin\src\data\charts"

    # Analyze all charts
    results = analyze_charts_directory(charts_directory)

    print(f"\n🎉 Analysis complete!")

if __name__ == "__main__":
    main()
