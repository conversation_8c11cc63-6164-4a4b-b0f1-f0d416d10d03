#!/usr/bin/env python3
"""
Chart Pattern Detector using Pre-trained Stock Market Model

This module integrates the foduucom/stockmarket-pattern-detection-yolov8 model
with our YOLO11 environment for detecting chart patterns in financial data.
"""

import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import mplfinance as mpf
from pathlib import Path
import time
import json
from typing import List, Dict, Tuple, Optional
from ultralytics import YOLO
import yfinance as yf
from datetime import datetime, timedelta

class ChartPatternDetector:
    """Detect chart patterns using the pre-trained stock market model."""
    
    def __init__(self, model_path: str = "models/stockmarket_patterns/model.pt"):
        """
        Initialize the chart pattern detector.
        
        Args:
            model_path (str): Path to the pre-trained model
        """
        self.model_path = Path(model_path)
        if not self.model_path.exists():
            raise FileNotFoundError(f"Model not found: {model_path}")
        
        # Load the pre-trained model
        print(f"🔄 Loading pre-trained model: {model_path}")
        self.model = YOLO(str(self.model_path))
        print("✅ Model loaded successfully!")
        
        # Pattern classes from the pre-trained model
        self.pattern_classes = [
            'Head and shoulders bottom',
            'Head and shoulders top', 
            'M_Head',
            'StockLine',
            'Triangle',
            'W_Bottom'
        ]
        
        # Pattern interpretations
        self.pattern_signals = {
            'Head and shoulders bottom': {'signal': 'bullish', 'strength': 'strong'},
            'Head and shoulders top': {'signal': 'bearish', 'strength': 'strong'},
            'M_Head': {'signal': 'bearish', 'strength': 'medium'},
            'StockLine': {'signal': 'neutral', 'strength': 'weak'},
            'Triangle': {'signal': 'neutral', 'strength': 'medium'},
            'W_Bottom': {'signal': 'bullish', 'strength': 'medium'}
        }
    
    def fetch_stock_data(self, symbol: str, period: str = "6mo", 
                        interval: str = "1d") -> pd.DataFrame:
        """
        Fetch stock data from Yahoo Finance.
        
        Args:
            symbol (str): Stock symbol (e.g., 'AAPL', 'MSFT')
            period (str): Data period ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
            interval (str): Data interval ('1m', '2m', '5m', '15m', '30m', '60m', '90m', '1h', '1d', '5d', '1wk', '1mo', '3mo')
            
        Returns:
            pd.DataFrame: Stock price data
        """
        print(f"📈 Fetching data for {symbol}...")
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period, interval=interval)
            
            if data.empty:
                raise ValueError(f"No data found for symbol {symbol}")
            
            print(f"✅ Fetched {len(data)} data points for {symbol}")
            return data
        
        except Exception as e:
            print(f"❌ Error fetching data for {symbol}: {e}")
            raise
    
    def create_chart_image(self, data: pd.DataFrame, symbol: str,
                          save_path: str, figsize: Tuple[int, int] = (12, 8)) -> str:
        """
        Create a candlestick chart image from stock data.
        
        Args:
            data (pd.DataFrame): Stock price data
            symbol (str): Stock symbol for title
            save_path (str): Path to save the chart image
            figsize (tuple): Figure size (width, height)
            
        Returns:
            str: Path to saved chart image
        """
        # Ensure save directory exists
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)
        
        # Create the chart
        fig, axes = mpf.plot(
            data,
            type='candle',
            style='charles',
            title=f'{symbol} - Stock Chart',
            ylabel='Price ($)',
            volume=True,
            figsize=figsize,
            returnfig=True,
            savefig=save_path
        )
        
        plt.close(fig)
        print(f"💾 Chart saved: {save_path}")
        return save_path
    
    def detect_patterns(self, image_path: str, 
                       confidence_threshold: float = 0.25) -> List[Dict]:
        """
        Detect chart patterns in an image.
        
        Args:
            image_path (str): Path to chart image
            confidence_threshold (float): Minimum confidence for detection
            
        Returns:
            List[Dict]: Detected patterns with details
        """
        print(f"🔍 Analyzing chart: {image_path}")
        
        # Run inference
        results = self.model(image_path, conf=confidence_threshold, save=True)
        
        detected_patterns = []
        
        if results and results[0].boxes is not None:
            boxes = results[0].boxes
            
            for i, box in enumerate(boxes):
                # Extract detection details
                class_id = int(box.cls[0])
                confidence = float(box.conf[0])
                bbox = box.xyxy[0].tolist()  # [x1, y1, x2, y2]
                
                pattern_name = self.pattern_classes[class_id]
                signal_info = self.pattern_signals[pattern_name]
                
                pattern_info = {
                    'pattern': pattern_name,
                    'confidence': confidence,
                    'bbox': bbox,
                    'signal': signal_info['signal'],
                    'strength': signal_info['strength'],
                    'timestamp': datetime.now().isoformat()
                }
                
                detected_patterns.append(pattern_info)
                
                print(f"  ✅ {pattern_name}: {confidence:.2f} ({signal_info['signal']})")
        
        else:
            print("  ❌ No patterns detected")
        
        return detected_patterns
    
    def analyze_stock(self, symbol: str, period: str = "6mo",
                     save_results: bool = True) -> Dict:
        """
        Complete analysis of a stock symbol.
        
        Args:
            symbol (str): Stock symbol
            period (str): Data period
            save_results (bool): Whether to save results
            
        Returns:
            Dict: Analysis results
        """
        print(f"🚀 Starting analysis for {symbol}")
        print("=" * 50)
        
        # Fetch stock data
        data = self.fetch_stock_data(symbol, period)
        
        # Create chart image
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        chart_path = f"results/charts/{symbol}_{timestamp}.png"
        self.create_chart_image(data, symbol, chart_path)
        
        # Detect patterns
        patterns = self.detect_patterns(chart_path)
        
        # Compile results
        analysis_results = {
            'symbol': symbol,
            'analysis_date': datetime.now().isoformat(),
            'data_period': period,
            'chart_path': chart_path,
            'patterns_detected': len(patterns),
            'patterns': patterns,
            'current_price': float(data['Close'].iloc[-1]),
            'price_change_1d': float(data['Close'].iloc[-1] - data['Close'].iloc[-2]) if len(data) > 1 else 0,
            'volume': int(data['Volume'].iloc[-1]) if 'Volume' in data.columns else 0
        }
        
        # Generate trading signals
        analysis_results['trading_signals'] = self.generate_trading_signals(patterns)
        
        # Save results if requested
        if save_results:
            results_path = f"results/analysis/{symbol}_{timestamp}.json"
            Path(results_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(results_path, 'w') as f:
                json.dump(analysis_results, f, indent=2)
            
            print(f"💾 Results saved: {results_path}")
        
        return analysis_results
    
    def generate_trading_signals(self, patterns: List[Dict]) -> Dict:
        """
        Generate trading signals based on detected patterns.
        
        Args:
            patterns (List[Dict]): Detected patterns
            
        Returns:
            Dict: Trading signals and recommendations
        """
        if not patterns:
            return {
                'overall_signal': 'neutral',
                'confidence': 0.0,
                'recommendation': 'No clear patterns detected. Monitor for developments.',
                'risk_level': 'low'
            }
        
        # Calculate weighted signals
        bullish_weight = 0
        bearish_weight = 0
        total_confidence = 0
        
        strength_multipliers = {'weak': 1, 'medium': 2, 'strong': 3}
        
        for pattern in patterns:
            confidence = pattern['confidence']
            strength = strength_multipliers[pattern['strength']]
            weight = confidence * strength
            
            if pattern['signal'] == 'bullish':
                bullish_weight += weight
            elif pattern['signal'] == 'bearish':
                bearish_weight += weight
            
            total_confidence += confidence
        
        # Determine overall signal
        if bullish_weight > bearish_weight * 1.2:  # 20% threshold
            overall_signal = 'bullish'
            signal_strength = bullish_weight / (bullish_weight + bearish_weight)
        elif bearish_weight > bullish_weight * 1.2:
            overall_signal = 'bearish'
            signal_strength = bearish_weight / (bullish_weight + bearish_weight)
        else:
            overall_signal = 'neutral'
            signal_strength = 0.5
        
        # Generate recommendation
        avg_confidence = total_confidence / len(patterns)
        
        if overall_signal == 'bullish' and avg_confidence > 0.6:
            recommendation = "Strong bullish patterns detected. Consider buying opportunities."
            risk_level = 'medium'
        elif overall_signal == 'bearish' and avg_confidence > 0.6:
            recommendation = "Strong bearish patterns detected. Consider selling or shorting."
            risk_level = 'medium'
        elif overall_signal == 'bullish':
            recommendation = "Weak bullish signals. Monitor for confirmation."
            risk_level = 'low'
        elif overall_signal == 'bearish':
            recommendation = "Weak bearish signals. Monitor for confirmation."
            risk_level = 'low'
        else:
            recommendation = "Mixed or neutral signals. Wait for clearer patterns."
            risk_level = 'low'
        
        return {
            'overall_signal': overall_signal,
            'confidence': signal_strength,
            'recommendation': recommendation,
            'risk_level': risk_level,
            'pattern_count': len(patterns),
            'avg_pattern_confidence': avg_confidence
        }
    
    def batch_analyze(self, symbols: List[str], period: str = "6mo") -> Dict:
        """
        Analyze multiple stocks in batch.
        
        Args:
            symbols (List[str]): List of stock symbols
            period (str): Data period
            
        Returns:
            Dict: Batch analysis results
        """
        print(f"🔄 Batch analyzing {len(symbols)} symbols...")
        
        batch_results = {
            'analysis_date': datetime.now().isoformat(),
            'symbols_analyzed': len(symbols),
            'results': {}
        }
        
        for i, symbol in enumerate(symbols, 1):
            print(f"\n📊 Analyzing {symbol} ({i}/{len(symbols)})")
            try:
                result = self.analyze_stock(symbol, period, save_results=False)
                batch_results['results'][symbol] = result
                print(f"✅ {symbol} analysis complete")
            except Exception as e:
                print(f"❌ Error analyzing {symbol}: {e}")
                batch_results['results'][symbol] = {'error': str(e)}
        
        # Save batch results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        batch_path = f"results/batch_analysis_{timestamp}.json"
        Path(batch_path).parent.mkdir(parents=True, exist_ok=True)
        
        with open(batch_path, 'w') as f:
            json.dump(batch_results, f, indent=2)
        
        print(f"\n🎉 Batch analysis complete! Results saved: {batch_path}")
        return batch_results

def main():
    """Example usage of the ChartPatternDetector."""
    # Initialize detector
    detector = ChartPatternDetector()
    
    # Analyze a single stock
    print("🔍 Single Stock Analysis Example")
    print("=" * 50)
    
    try:
        # Analyze Apple stock
        results = detector.analyze_stock("AAPL", period="3mo")
        
        print(f"\n📊 Analysis Results for AAPL:")
        print(f"Current Price: ${results['current_price']:.2f}")
        print(f"Patterns Detected: {results['patterns_detected']}")
        print(f"Overall Signal: {results['trading_signals']['overall_signal']}")
        print(f"Recommendation: {results['trading_signals']['recommendation']}")
        
    except Exception as e:
        print(f"❌ Error in analysis: {e}")
    
    # Batch analysis example
    print("\n🔍 Batch Analysis Example")
    print("=" * 50)
    
    try:
        # Analyze multiple stocks
        symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]
        batch_results = detector.batch_analyze(symbols, period="3mo")
        
        print(f"\n📊 Batch Analysis Summary:")
        for symbol, result in batch_results['results'].items():
            if 'error' not in result:
                signal = result['trading_signals']['overall_signal']
                patterns = result['patterns_detected']
                print(f"  {symbol}: {signal} signal, {patterns} patterns")
            else:
                print(f"  {symbol}: Error - {result['error']}")
                
    except Exception as e:
        print(f"❌ Error in batch analysis: {e}")

if __name__ == "__main__":
    main()
